import { useCallback, useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { setDateMaxHours, setDateMinHours } from 'helpers/data/setHoursDate';
import { formatOptionsSelectClient } from 'helpers/format/formatSelectClient';
import { ImprimirPDF } from 'helpers/impressoes/imprimirPDF';
import { validarListaServicos } from 'helpers/validation/validarListaServicos';

import api, { ResponseApi } from 'services/api';

import { obterListaSelectLocalEstoque } from 'api/LocalEstoque/ObterListaSelect';
import { gerarRelatorioVendas } from 'api/RelatoriosProduto/Vendas/GerarRelatorio';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import {
  EnumApisRelatorioVendas,
  EnumRelatorioVendas,
} from 'constants/enum/enumRelatorioVendas';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import { StatusPesquisaClientesFornecedor } from 'constants/enum/statusPesquisaClientesFornecedor';

import { adaptarGerarRelatorioVendas } from './adapters/index';
import { validarTipoRelatorioVendas } from './ValidarTipoRelatorioVendas';
import {
  OptionsProps,
  SelectClienteProps,
  OptionResponseProps,
  FormData,
} from './validationForms';

export const useRelatorioVendas = (
  formMethods: UseFormReturn<FormData, object>
) => {
  const [isLoading, setIsLoading] = useState(false);
  const [listaVendedores, setListaVendedores] = useState<OptionsProps[]>([]);

  const { id: lojaId } = auth.getLoja();
  const { watch, handleSubmit, setValue, clearErrors } = formMethods;
  const { tipoRelatorio, inclusoHorasNoPeriodo } = watch();

  const possuiServicoFrenteCaixa = validarListaServicos([
    enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
    enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
  ]);

  const isRelatorioVendaSimplificada =
    tipoRelatorio === EnumRelatorioVendas.VENDAS_SIMPLIFICADAS;

  const naoExibirCampoEntregador =
    tipoRelatorio === EnumRelatorioVendas.VENDAS_TOTALIZADAS_VENDEDORES ||
    tipoRelatorio ===
      EnumRelatorioVendas.GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES ||
    tipoRelatorio ===
      EnumRelatorioVendas.GRAFICO_VENDAS_VENDEDORES_FRENTE_CAIXA;

  const listaTipoRelatoriosFiltradosPermissao =
    EnumRelatorioVendas.properties.filter((relatorioItem) =>
      validarTipoRelatorioVendas(relatorioItem)
    );

  const listaTipoRelatoriosFiltradosServicos =
    listaTipoRelatoriosFiltradosPermissao.filter((relatorioItem) => {
      return relatorioItem.exibir === undefined || relatorioItem.exibir;
    });

  const obterEndpointPorTipoRelatorio = () => {
    const relatorioVendasFormaRecebimento =
      tipoRelatorio === EnumRelatorioVendas.VENDAS_FORMA_RECEBIMENTO;

    const endpoint =
      relatorioVendasFormaRecebimento || tipoRelatorio
        ? EnumApisRelatorioVendas[tipoRelatorio]
        : '';

    return endpoint;
  };

  const getClientes = useCallback(async (inputValue?: string) => {
    const response = await api.get<void, ResponseApi<SelectClienteProps[]>>(
      ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_LISTAR_SELECT,
      {
        params: {
          filtroTipoCadastroPessoa: StatusPesquisaClientesFornecedor.CLIENTES,
          cpfCnpjNomeApelidoCodigoExterno: inputValue,
        },
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response.sucesso && response.dados) {
        const dados = response.dados.map((cliente) => {
          const option = formatOptionsSelectClient(cliente);
          return option;
        });
        return dados;
      }
    }

    return [];
  }, []);

  const buscarListaVendedores = useCallback(async () => {
    const response = await api.get<void, ResponseApi<OptionResponseProps[]>>(
      ConstanteEnderecoWebservice.VENDEDOR_LISTAR_SELECT_POR_LOJA
    );

    if (response?.avisos) {
      response.avisos.forEach((item) => toast.warn(item));
    }

    if (response?.sucesso && response?.dados) {
      const vendedores = response.dados.map((vendedor) => ({
        label: vendedor.nome,
        value: vendedor.id,
      }));

      setListaVendedores(vendedores);
    }
  }, []);

  const buscarListaEntregadores = useCallback(async (inputValue: string) => {
    const response = await api.get<
      void,
      ResponseApi<{ id: string; nome: string }[]>
    >(ConstanteEnderecoWebservice.ENTREGADOR_LISTAR_SELECT, {
      params: { pesquisa: inputValue || '' },
    });

    if (response?.sucesso && response?.dados) {
      const listaEntregadores = response.dados.map((item) => ({
        label: item.nome,
        value: item.id,
      }));

      return listaEntregadores;
    }

    return [];
  }, []);

  const gerarRelatorio = async (dados: FormData) => {
    const endpoint = obterEndpointPorTipoRelatorio();
    const relatorioAdaptado = adaptarGerarRelatorioVendas(dados);

    const response = await gerarRelatorioVendas({
      endpoint,
      relatorio: relatorioAdaptado,
    });

    return response;
  };

  const imprimirRelatorio = (relatorio: string) => {
    ImprimirPDF(relatorio, 'relatorioVendas');
  };

  const handleGerarRelatorio = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await gerarRelatorio(data);

    if (response?.avisos) {
      response.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      imprimirRelatorio(response.dados);
    }

    setIsLoading(false);
  });

  const obterOpcoesLocaisDeEstoque = useCallback(async () => {
    const response = await obterListaSelectLocalEstoque({ lojaId });

    if (response?.avisos) {
      response?.avisos.forEach((aviso) => toast.warning(aviso));
    }

    if (response?.sucesso && response?.dados) {
      const opcoes = response.dados.map((local) => ({
        value: local.id,
        label: local.nome,
      }));

      return opcoes;
    }

    return [];
  }, [lojaId]);

  const formatarData = (dataOriginal: string, incluirHoras: boolean) => {
    if (!dataOriginal || isNaN(Date.parse(dataOriginal))) {
      return null;
    }

    const data = new Date(dataOriginal);

    if (incluirHoras) {
      data.setUTCHours(data.getUTCHours() - 3);
      return new Date(data).toISOString().slice(0, 16);
    }

    return new Date(`${data.toISOString()}`);
  };

  const handleFormatarDatas = ({ incluirHoras }: { incluirHoras: boolean }) => {
    const dataEmissaoInicio = watch('dataEmissaoInicio') as any;
    const dataEmissaoFim = watch('dataEmissaoFim') as any;

    const dataInicioFormatada = formatarData(
      dataEmissaoInicio,
      incluirHoras
    ) as any;
    const dataFimFormatada = formatarData(dataEmissaoFim, incluirHoras) as any;

    if (!dataInicioFormatada || !dataFimFormatada) {
      return;
    }
    const valorDataInicio = incluirHoras
      ? dataInicioFormatada
      : setDateMinHours(dataInicioFormatada as Date);

    const valorDataFim = incluirHoras
      ? dataFimFormatada
      : setDateMaxHours(dataFimFormatada as Date);

    setValue('dataEmissaoInicio', valorDataInicio);
    setValue('dataEmissaoFim', valorDataFim);
  };

  const resetarErrosInputsDataHora = () => {
    clearErrors('dataEmissaoInicio');
    clearErrors('dataEmissaoFim');
  };

  useEffect(() => {
    buscarListaVendedores();
  }, [buscarListaVendedores]);

  useEffect(() => {
    if (isRelatorioVendaSimplificada) {
      return;
    }

    setValue('statusConsulta', undefined);
  }, [isRelatorioVendaSimplificada, setValue]);

  return {
    isLoading,
    isRelatorioVendaSimplificada,
    listaTipoRelatoriosFiltradosServicos,
    getClientes,
    listaVendedores,
    possuiServicoFrenteCaixa,
    naoExibirCampoEntregador,
    buscarListaEntregadores,
    obterOpcoesLocaisDeEstoque,
    handleGerarRelatorio,
    inclusoHorasNoPeriodo,
    handleFormatarDatas,
    resetarErrosInputsDataHora,
  };
};
