import { But<PERSON>, Stack, useMediaQuery } from '@chakra-ui/react';

import { ModalAvisoGerarTroca } from 'components/PDV/Modal/ModalAvisoGerarTroca/index';

import { ClienteListarProps } from '../Types/validationForm';

type TrocasFooterProps = {
  handleAbrirModalDevolverDinheiro: () => void;
  existeProdutoTrocaSelecionado: boolean;
  handleGerarTroca: () => void;
  handleGerarVoucher: () => void;
  cliente: ClienteListarProps;
  valorTotal: number;
  isLoading: boolean;
};

export function TrocasFooter({
  handleGerarTroca,
  handleGerarVoucher,
  isLoading,
  existeProdutoTrocaSelecionado,
  handleAbrirModalDevolverDinheiro,
  cliente,
  valorTotal,
}: TrocasFooterProps) {
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const handleAbrirModalAvisoGerarTroca = () => {
    ModalAvisoGerarTroca({
      nomeCliente: cliente.nome,
      valorTotalTroca: valorTotal,
      handleGerarTroca,
    });
  };

  return (
    <Stack
      mr={isLargerThan900 ? '40px' : '0'}
      mt={isLargerThan900 ? '0' : '24px'}
      direction={isLargerThan900 ? 'row' : 'column-reverse'}
      align={isLargerThan900 ? 'right' : 'center'}
      justifyContent={isLargerThan900 ? 'right' : 'center'}
      h="100%"
      spacing={4}
    >
      <Button
        id="devolverDinheiro"
        name="devolverDinheiro"
        isDisabled={!existeProdutoTrocaSelecionado}
        borderRadius="20px"
        fontSize={isLargerThan900 ? 'sm' : 'md'}
        type="button"
        variant="outline"
        _hover={{ bg: 'gray.50', color: 'black' }}
        onClick={() => handleAbrirModalDevolverDinheiro()}
        h={isLargerThan900 ? '32px' : '40px'}
        width={isLargerThan900 ? '136px' : 'full'}
      >
        Devolver dinheiro
      </Button>
      <Button
        id="gerarCredito"
        name="gerarCredito"
        isLoading={isLoading}
        isDisabled={!existeProdutoTrocaSelecionado}
        borderRadius="20px"
        fontSize={isLargerThan900 ? 'sm' : 'md'}
        type="button"
        variant="outline"
        _hover={{ bg: 'gray.50', color: 'black' }}
        onClick={() => handleGerarVoucher()}
        h={isLargerThan900 ? '32px' : '40px'}
        width={isLargerThan900 ? '160px' : 'full'}
      >
        Gerar crédito voucher
      </Button>
      <Button
        id="trocarProduto"
        name="trocarProduto"
        color="black"
        isDisabled={!existeProdutoTrocaSelecionado}
        borderRadius="20px"
        onClick={() => handleAbrirModalAvisoGerarTroca()}
        fontSize={isLargerThan900 ? 'sm' : 'md'}
        type="button"
        variant="solid"
        colorScheme="secondary"
        h={isLargerThan900 ? '32px' : '40px'}
        w={isLargerThan900 ? '140px' : 'full'}
      >
        Trocar produtos
      </Button>
    </Stack>
  );
}
