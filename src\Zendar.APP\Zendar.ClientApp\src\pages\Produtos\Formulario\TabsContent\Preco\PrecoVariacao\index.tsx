import {
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Flex,
  Text,
  Box,
  Button,
} from '@chakra-ui/react';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { moneyMask } from 'helpers/format/fieldsMasks';

import { useProdutosFormularioContext } from 'store/Produtos/ProdutosFormulario';

import {
  ModalAdicionarPrecoEspecial,
  VariacaoProps,
} from 'components/Modal/ModalAdicionarVariacao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';

import { SalvarInserirNovoIcon } from 'icons';

type PrecoVariacaoProps = {
  setValoresIniciaisAlterados: React.Dispatch<React.SetStateAction<boolean>>;
};

export const PrecoVariacao = ({
  setValoresIniciaisAlterados,
}: PrecoVariacaoProps) => {
  const { formFields, idProduto, tabelaPreco } = useProdutosFormularioContext();
  const { fields, append, remove, update } = formFields;
  const { getValues } = useFormContext();

  const { id } = auth.getLoja();

  const adicionarPrecoEspecialResetarModal = (
    novoPrecoEspecial: VariacaoProps
  ) => {
    console.log('=== DEBUG VALIDAÇÃO ===');
    console.log('Campos atuais:', formFields.fields);
    console.log('Novo preço especial:', novoPrecoEspecial);

    // Valida duplicidade com o estado atual antes de adicionar
    const existeVariacaoComMesmaTabelaPrecoEProdutoCorTamanhoId =
      formFields.fields.some((item) => {
        return novoPrecoEspecial?.some((novoItem) => {
          const isDuplicate =
            item.tabelaPrecoId === novoItem.tabelaPrecoId &&
            item.produtoCorTamanhoId === novoItem.produtoCorTamanhoId;

          if (isDuplicate) {
            console.log('Duplicata encontrada:', {
              existente: {
                tabelaPrecoId: item.tabelaPrecoId,
                produtoCorTamanhoId: item.produtoCorTamanhoId,
              },
              novo: {
                tabelaPrecoId: novoItem.tabelaPrecoId,
                produtoCorTamanhoId: novoItem.produtoCorTamanhoId,
              },
            });
          }

          return isDuplicate;
        });
      });

    console.log(
      'Existe duplicata?',
      existeVariacaoComMesmaTabelaPrecoEProdutoCorTamanhoId
    );

    if (existeVariacaoComMesmaTabelaPrecoEProdutoCorTamanhoId) {
      toast.warning(
        'Não é possível cadastrar mais de um preço especial para a mesma variação e tabela de preços.'
      );
      return false;
    }

    setValoresIniciaisAlterados(true);
    append(novoPrecoEspecial);
    console.log('Preço adicionado com sucesso');
    return true;
  };

  const validarDuplicidadePrecoEspecial = useCallback(
    (novoPrecoEspecial: VariacaoProps) => {
      // Para o botão "Confirmar e sair", ainda precisamos validar
      // pois a validação do "Confirmar e adicionar outro" é feita em adicionarPrecoEspecialResetarModal
      const camposAtuais = formFields.fields;
      const existeVariacaoComMesmaTabelaPrecoEProdutoCorTamanhoId =
        camposAtuais.some((item) => {
          return novoPrecoEspecial?.some(
            (novoItem) =>
              item.tabelaPrecoId === novoItem.tabelaPrecoId &&
              item.produtoCorTamanhoId === novoItem.produtoCorTamanhoId
          );
        });

      if (existeVariacaoComMesmaTabelaPrecoEProdutoCorTamanhoId) {
        toast.warning(
          'Não é possível cadastrar mais de um preço especial para a mesma variação e tabela de preços.'
        );
        return false;
      }
      return true;
    },
    [formFields.fields]
  );

  const handleAdicionarPrecoEspecial = async (
    estaAlterando?: boolean,
    index?: number
  ) => {
    const precoAtual = getValues(`valuePrecoVenda.${id}`);
    const custo = getValues(`valuePrecoCusto.${id}`);
    const dados = fields[index || 0];
    const novoPrecoEspecial = await ModalAdicionarPrecoEspecial({
      idProduto,
      precoAtual,
      custo,
      tabelaPreco: tabelaPreco.map((item) => ({
        value: item.id,
        label: item.nome,
      })),
      isAlterar: estaAlterando || false,
      dados,
      confirmarResetar: adicionarPrecoEspecialResetarModal,
      validarDuplicidade: validarDuplicidadePrecoEspecial,
    });

    if (estaAlterando) {
      setValoresIniciaisAlterados(true);
      const dadosModal = novoPrecoEspecial[0];
      update(index || 0, {
        ...dadosModal,
      });
    } else {
      setValoresIniciaisAlterados(true);
      append(novoPrecoEspecial);
    }
  };

  const handleRemoverVariacao = (index: number) => {
    const newListVariacao = [...fields];
    setValoresIniciaisAlterados(true);
    newListVariacao.splice(index, 1);

    remove(index);
  };

  return (
    <>
      {fields.length > 0 ? (
        <Table variant="none" minW="900px">
          <Thead>
            <Tr
              sx={{
                '& > th': {
                  color: 'gray.500',
                  fontWeight: 'bold',
                  paddingTop: '0 !important',
                  paddingBottom: '0',
                  fontSize: '12px',
                  paddingLeft: '0 !important',
                },
              }}
            >
              <Th w="40%">
                <Button
                  type="button"
                  variant="solid"
                  colorScheme="blue"
                  leftIcon={<SalvarInserirNovoIcon />}
                  marginBottom="4px"
                  maxWidth="200px"
                  maxHeight="32px"
                  w="100%"
                  fontStyle="italic"
                  fontSize="14px"
                  onClick={() => handleAdicionarPrecoEspecial(false, 0)}
                  fontWeight="normal"
                >
                  Adicionar novo preço
                </Button>
              </Th>
              <Th whiteSpace="nowrap" w="20%">
                <Flex justifyContent="flex-end">Preço especial</Flex>
              </Th>
              <Th whiteSpace="nowrap" w="17%">
                <Flex justifyContent="flex-end">Markup especial</Flex>
              </Th>
              <Th whiteSpace="nowrap" w="23%">
                <Flex px="22px" justifyContent="flex-start">
                  Tabela de preços
                </Flex>
              </Th>
              <Th w="1%" />
            </Tr>
          </Thead>
          <Tbody>
            {fields.map((variacaoItem, index) => (
              <>
                <Tr
                  sx={{
                    boxShadow: '0px 0px 4px #00000029',
                    borderRadius: '6px',
                    _hover: {
                      boxShadow: '0px 0px 4px rgba(85, 2, 178, 0.5)',
                    },
                    '& > td': {
                      height: '52px !important',
                    },
                  }}
                >
                  <Td
                    borderStartRadius="6px"
                    bg="white"
                    display="flex"
                    fontSize="14px"
                  >
                    <Text color="teal.600">
                      {variacaoItem.produtoCorTamanho.cor}
                    </Text>
                    {variacaoItem.produtoCorTamanho.cor &&
                      variacaoItem.produtoCorTamanho.tamanho && (
                        <Text mx="5px">|</Text>
                      )}
                    <Text color="pink.600">
                      {variacaoItem.produtoCorTamanho.tamanho}
                    </Text>
                  </Td>
                  <Td bg="white" isNumeric>
                    <Flex justifyContent="flex-end">
                      {moneyMask(variacaoItem.precoVenda.precoVenda, true)}
                    </Flex>
                  </Td>
                  <Td bg="white" isNumeric>
                    <Flex justifyContent="flex-end">
                      {moneyMask(variacaoItem.precoVenda.markup, false)}%
                    </Flex>
                  </Td>
                  <Td bg="white">
                    <Flex justifyContent="flex-start">
                      {variacaoItem.tabelaPreco}
                    </Flex>
                  </Td>
                  <Td borderEndRadius="6px" bg="white" isNumeric>
                    <ActionsMenu
                      items={[
                        {
                          content: 'Editar',
                          onClick: () =>
                            handleAdicionarPrecoEspecial(true, index),
                        },
                        {
                          content: 'Remover',
                          onClick: () => handleRemoverVariacao(index),
                        },
                      ]}
                    />
                  </Td>
                </Tr>
                <Box h="4px" />
              </>
            ))}
          </Tbody>
        </Table>
      ) : (
        <>
          <Button
            type="button"
            variant="solid"
            colorScheme="blue"
            leftIcon={<SalvarInserirNovoIcon />}
            marginBottom="4px"
            maxWidth="200px"
            maxHeight="32px"
            w="100%"
            fontStyle="italic"
            fontSize="14px"
            onClick={() => handleAdicionarPrecoEspecial(false, 0)}
            fontWeight="normal"
          >
            Adicionar novo preço
          </Button>
          <Box
            bg="white"
            minHeight="64px"
            p={['12px', '22px 40px']}
            borderRadius="5px"
            borderColor="gray.200"
            borderWidth="1px"
          >
            Não existem preços especiais para este produto.
          </Box>
        </>
      )}
    </>
  );
};
