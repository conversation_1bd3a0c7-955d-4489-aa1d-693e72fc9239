import { useMediaQuery } from '@chakra-ui/react';
import { useCallback, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ProdutosResponseProps, usePromocaoContext } from 'store/Promocao';

import { PaginationData } from 'components/update/Pagination';
import { ExpandableSearchMode } from 'components/v2/ui/ExpandableSearch/types';

import { ObterProdutosDaPromocao } from 'api/Promocao/ObterProdutos';

import { useLoadingManager } from '../../../hooks';

export const useProdutoPaginado = () => {
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [modoPesquisa, setModoPesquisa] =
    useState<ExpandableSearchMode>('botao');
  const [listarProdutos, setListarProdutos] = useState<ProdutosResponseProps[]>(
    []
  );

  const valorPesquisado = useRef<string | undefined>('');

  const { pagedTableRef } = usePromocaoContext();
  const { isLoading, setIsLoading } = useLoadingManager();
  const [maiorQue900] = useMediaQuery('(min-width: 900px)');

  const { id: promocaoId } = useParams<{ id: string }>();

  const handlePaginacao = useCallback(
    async (dadosPaginacao: PaginationData) => {
      setIsLoading(true);

      try {
        const response = await ObterProdutosDaPromocao({
          promocaoId,
          dadosPaginacao,
          parametros: { pesquisa: valorPesquisado.current },
        });

        if (response) {
          if (response.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
          }
          if (response.sucesso) {
            setTotalRegistros(response.dados.total);
            setListarProdutos(response.dados.registros);
          }
        }
      } finally {
        setIsLoading(false);
      }
    },
    [promocaoId, setIsLoading]
  );

  const atualizarListagem = async () => {
    handlePaginacao({
      currentPage: 1,
      orderColumn: '',
      orderDirection: 'asc',
      pageSize: 10,
    });
  };

  const handleMudarValorPesquisado = (valor?: string) => {
    valorPesquisado.current = valor;
  };

  return {
    listarProdutos,
    totalRegistros,
    isLoading,
    handlePaginacao,
    pagedTableRef,
    atualizarListagem,
    setModoPesquisa,
    modoPesquisa,
    maiorQue900,
    handleMudarValorPesquisado,
  };
};
