import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

export type ObterSaldoVariacoesDoProdutoRetorno = {
  itensConsignados: {
    data: string;
    variacao: string;
    cliente: string;
    telefone: string;
    quantidade: number;
  }[];
  saldoVariacoes: {
    cor: string;
    localEstoque: string;
    localEstoqueId: string;
    loja: string;
    lojaId: string;
    produtoCorId: string;
    produtoCorTamanhoId: string;
    saldo: number;
    tamanho: string;
  }[];
};

export type ObterSaldoVariacoesDoProdutoParametros = {
  produtoId: string;
  corId?: string;
};

export const obterSaldoVariacoesDoProduto = (
  parametros: ObterSaldoVariacoesDoProdutoParametros
) => {
  return api.get<void, ResponseApi<ObterSaldoVariacoesDoProdutoRetorno>>(
    ConstanteEnderecoWebservice.PRODUTO_OBTER_SALDO_VARIACOES,
    {
      params: parametros,
    }
  );
};
