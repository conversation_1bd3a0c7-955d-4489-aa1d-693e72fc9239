import auth from 'modules/auth';

import { EnumRelatorioVendas } from 'constants/enum/enumRelatorioVendas';
import ConstanteFuncionalidades from 'constants/permissoes';

export const validarTipoRelatorioVendas = (item: {
  value: number;
}): boolean => {
  let tipoRelatorio = '';
  const enumRelatorio = EnumRelatorioVendas;

  switch (item.value) {
    case enumRelatorio.VENDAS_FORMA_RECEBIMENTO:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_VENDAS_POR_FORMA_RECEBIMENTO;
      break;
    case enumRelatorio.VENDAS_POR_DIA:
      tipoRelatorio = ConstanteFuncionalidades.RELATORIO_VENDAS_POR_DIA;
      break;
    case enumRelatorio.VENDAS_SIMPLIFICADAS:
      tipoRelatorio = ConstanteFuncionalidades.RELATORIO_VENDA_SIMPLIFICADAS;
      break;
    case enumRelatorio.GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES;
      break;
    case enumRelatorio.VENDAS_TOTALIZADAS_PRODUTOS:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_VENDAS_TOTALIZADAS_PRODUTOS;
      break;

    case enumRelatorio.DETALHAMENTO_ENTREGAS_ENTREGADOR:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_DETALHAMENTO_ENTREGAS_ENTREGADOR;
      break;
    case enumRelatorio.RESUMO_ENTREGAS_ENTREGADOR:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_RESUMO_ENTREGAS_ENTREGADOR;
      break;
    case enumRelatorio.VALORES_ADICIONAIS_COBRADOS_VENDAS:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_VALORES_ADICIONAIS_COBRADOS_VENDAS;
      break;
    case enumRelatorio.VENDAS_TOTALIZADAS_VENDEDORES:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_VENDAS_TOTALIZADAS_VENDEDORES;
      break;
    case enumRelatorio.LUCRO_AGRUPADO_POR_DIA:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_VENDAS_LUCRO_AGRUPADO_POR_DIA;
      break;

    case enumRelatorio.TOTALIZACAO_VENDAS_PRODUTO:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_TOTALIZACAO_VENDAS_PRODUTO;
      break;

    case enumRelatorio.GRAFICO_VENDAS_VENDEDORES_FRENTE_CAIXA:
      tipoRelatorio =
        ConstanteFuncionalidades.RELATORIO_GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES_FRENTE_CAIXA;
      break;

    default:
      break;
  }

  const possuiFuncionalidade = auth.possuiPermissao(tipoRelatorio).permitido;

  return possuiFuncionalidade;
};
