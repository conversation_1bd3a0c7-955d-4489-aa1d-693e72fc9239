import {
  ModalProps,
  ModalContent,
  ModalBody,
  Button,
  useDisclosure,
  Text,
  Flex,
  ModalFooter,
  ModalHeader,
  useMediaQuery,
  Divider,
  GridItem,
  VStack,
  FormLabel,
} from '@chakra-ui/react';
import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { NumberInput } from 'components/update/Input/NumberInput';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteFuncionalidades from 'constants/permissoes';

import ListaVariacoesVirtualizada from './ListaVariacoesVirtualizada';

type FormData = {
  markup: number | string;
  precoNovo: number;
  precoAtual: number;
  custo: number;
  tabelaPreco: { label: string; value: string } | null;
};

type VariacaoResponseProps = {
  produtoCorTamanhoId: string;
  produto: string;
  cor: string;
  tamanho: string;
  imagem: string;
  isChecked: boolean;
};

export type Variacao = {
  produtoCorTamanho: {
    produtoCorTamanhoId: string;
    produto: string;
    cor: string;
    tamanho: string;
  };
  precoVenda: {
    precoVenda: number;
    markup: number;
  };
  tabelaPreco: string;
  produtoCorTamanhoId: string;
  tabelaPrecoId: string;
  isChecked: boolean;
};

export type VariacaoProps = Variacao[];

type Options = {
  label: string;
  value: string;
};

export type OptionResponseProps = {
  id: string;
  nome: string;
  padraoSistema: boolean;
};

type ModalAdicionarVariacaoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<VariacaoProps> & {
    idProduto: string;
    precoAtual: number;
    custo: number;
    tabelaPreco: Options[];
    isAlterar?: boolean;
    dados?: Variacao | undefined;
    confirmarResetar: (novoPrecoEspecial: VariacaoProps) => void;
    validarDuplicidade: (novoPrecoEspecial: VariacaoProps) => boolean;
  };

export const ModalAdicionarPrecoEspecial = create<
  ModalAdicionarVariacaoProps,
  VariacaoProps
>(
  ({
    onResolve,
    onReject,
    idProduto,
    isAlterar = false,
    custo,
    precoAtual,
    dados,
    tabelaPreco,
    confirmarResetar,
    validarDuplicidade,
    ...rest
  }) => {
    const [variacoesProduto, setVariacoesProduto] = useState<
      VariacaoResponseProps[]
    >([]);
    const [isLoading, setIsLoading] = useState(false);

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
    const [isSmallerThan1200] = useMediaQuery('(max-width: 1200px)');

    const valueMarkup =
      precoAtual > 0 && custo > 0 ? (precoAtual / custo - 1) * 100 : 0;

    const formMethods = useForm<FormData>({
      defaultValues: {
        custo,
        precoAtual,
        markup: valueMarkup.toFixed(2),
        precoNovo: 0,
      },
    });

    const possuiPermissaoVisualizarPrecoCusto = auth.possuiPermissao(
      ConstanteFuncionalidades.USUARIO_VISUALIZAR_PRECO_CUSTO
    ).permitido;

    const { handleSubmit, getValues, setValue, reset, watch } = formMethods;

    const possuiTabelaPreco = watch('tabelaPreco');
    const precoNovo = watch('precoNovo');

    const possuiVariacaoSelecionada = variacoesProduto?.some(
      (variacao) => variacao.isChecked
    );

    const camposHabilitados = possuiTabelaPreco && possuiVariacaoSelecionada;

    const buscarVariacoes = useCallback(async () => {
      setIsLoading(true);
      const response = await api.get<
        void,
        ResponseApi<VariacaoResponseProps[]>
      >(
        `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${idProduto}/produtoCorTamanhos`
      );
      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }

        if (response.sucesso) {
          setVariacoesProduto(response.dados);
        }
      }
      setIsLoading(false);
    }, [idProduto]);

    const handleModificarMarkup = useCallback(() => {
      const precoVenda = getValues('precoNovo');

      if (precoVenda === 0) {
        return;
      }

      const newMarkup = (precoVenda / custo - 1) * 100;

      if (newMarkup === Infinity) {
        return;
      }

      setValue('markup', newMarkup.toFixed(2));
    }, [custo, getValues, setValue]);

    const adapterData = useCallback(
      (data: FormData) => {
        const resolveData = variacoesProduto
          .filter((item) => item.isChecked)
          .map((variacao) => ({
            tabelaPreco: data?.tabelaPreco?.label,
            produtoCorTamanhoId: variacao.produtoCorTamanhoId,
            tabelaPrecoId: data.tabelaPreco?.value,
            produtoCorTamanho: {
              produtoCorTamanhoId: variacao.produtoCorTamanhoId,
              produto: '',
              cor: variacao.cor,
              tamanho: variacao.tamanho,
            },
            precoVenda: {
              precoVenda: data.precoNovo,
              markup: data.markup,
            },
            isChecked: true,
          })) as VariacaoProps;

        return resolveData;
      },
      [variacoesProduto]
    );

    const limparCampos = useCallback(() => {
      reset({
        custo,
        precoAtual,
        markup: valueMarkup.toFixed(2),
        precoNovo: 0,
        tabelaPreco: null,
      });
      setVariacoesProduto((prev) =>
        prev.map((item) => ({
          ...item,
          isChecked: false,
        }))
      );
    }, [custo, precoAtual, reset, valueMarkup]);

    const handleConfirmaSair = useCallback(
      (data: FormData) => {
        setIsLoading(true);
        const novoPrecoEspecial = adapterData(data);
        if (!isAlterar && !validarDuplicidade(novoPrecoEspecial)) {
          setIsLoading(false);
          return;
        }
        onResolve(novoPrecoEspecial);
        setIsLoading(false);
        onClose();
      },
      [adapterData, onClose, onResolve, validarDuplicidade, isAlterar]
    );

    const handleConfirmarResetar = useCallback(
      (data: FormData) => {
        setIsLoading(true);
        const novoPrecoEspecial = adapterData(data);
        debugger;
        if (!isAlterar && !validarDuplicidade(novoPrecoEspecial)) {
          setIsLoading(false);
          return;
        }
        confirmarResetar(novoPrecoEspecial);
        limparCampos();
        setIsLoading(false);
      },
      [
        adapterData,
        confirmarResetar,
        limparCampos,
        validarDuplicidade,
        isAlterar,
      ]
    );

    useEffect(() => {
      if (isAlterar) {
        reset({
          custo,
          markup: dados?.precoVenda?.markup,
          precoAtual,
          precoNovo: dados?.precoVenda.precoVenda,
          tabelaPreco: {
            label: dados?.tabelaPreco,
            value: dados?.tabelaPrecoId,
          },
        });
        setVariacoesProduto([
          {
            cor: dados?.produtoCorTamanho.cor || '',
            imagem: '',
            isChecked: true,
            produto: '',
            produtoCorTamanhoId: dados?.produtoCorTamanhoId || '',
            tamanho: dados?.produtoCorTamanho.tamanho || '',
          },
        ]);
      } else {
        buscarVariacoes();
      }
    }, [buscarVariacoes, reset, isAlterar, dados, custo, precoAtual]);

    return (
      <ModalPadraoChakra
        isCentered={!isSmallerThan1200}
        size={!isSmallerThan900 ? '6xl' : 'full'}
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent px="0px" bg="gray.50" w="1080px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader pt="16px" pb={['20px', '20px', '28px']} pl="24px">
            <Text color="primary.50" fontSize="16px">
              Adicionar variação com preço especial
            </Text>

            <Divider mt="16px" />
          </ModalHeader>

          <ModalBody pl="24px" pr="24px" mb="20px" pt="0" pb="0">
            <FormProvider {...formMethods}>
              <Flex direction={['column', 'column', 'row']}>
                <GridItem h="full" w={['full', 'full', '50%']} colSpan={12}>
                  <FormLabel mb="0px" fontSize="14px">
                    Selecione as variações
                  </FormLabel>
                  <ListaVariacoesVirtualizada
                    listaVariacoes={variacoesProduto}
                    setListaVariacoes={setVariacoesProduto}
                    isAlterar={isAlterar}
                  />
                </GridItem>
                <VStack
                  ml={['0', '0', '24px']}
                  spacing={['20px', '20px', '28px']}
                  flex="1"
                  mt={['20px', '20px', '5px']}
                >
                  <SelectPadrao
                    label="Selecione uma tabela de preços"
                    required
                    asControlledByObject
                    id="tabelaPreco"
                    helperText="O cadastro do produto armazena apenas um preço por produto, portanto o valor especial da variação ficará salvo dentro da tabela de preços selecionada."
                    options={tabelaPreco}
                    isDisabled={isAlterar}
                    placeholder="Selecione a tabela de preços"
                    name="tabelaPreco"
                  />
                  <NumberInput
                    name="custo"
                    id="custo"
                    label="Custo"
                    w="full"
                    isDisabled
                    editarFundoLeftElemento
                    bgLeftElement="gray.50"
                    leftElement="R$"
                    leftElementColor="black"
                    leftElementFontSize="xs"
                    esconderValor={!possuiPermissaoVisualizarPrecoCusto}
                    scale={2}
                    colSpan={3}
                  />
                  <NumberInput
                    name="precoAtual"
                    id="precoAtual"
                    label="Preço atual do produto"
                    isDisabled
                    w="full"
                    editarFundoLeftElemento
                    bgLeftElement="gray.50"
                    leftElement="R$"
                    leftElementColor="black"
                    leftElementFontSize="xs"
                    scale={2}
                    colSpan={3}
                  />
                  <NumberInput
                    name="precoNovo"
                    id="precoNovo"
                    w="full"
                    isDisabled={!camposHabilitados}
                    label="Preço novo especial"
                    editarFundoLeftElemento
                    bgLeftElement="gray.50"
                    leftElement="R$"
                    leftElementColor="black"
                    leftElementFontSize="xs"
                    scale={2}
                    onBlur={handleModificarMarkup}
                    colSpan={3}
                  />
                  <NumberInput
                    name="markup"
                    id="markup"
                    label="Markup"
                    w="full"
                    isDisabled
                    leftElement="%"
                    editarFundoLeftElemento
                    leftElementColor="black"
                    bgLeftElement="gray.50"
                    leftElementFontSize="xs"
                    esconderValor={!possuiPermissaoVisualizarPrecoCusto}
                    scale={2}
                    colSpan={3}
                  />
                </VStack>
              </Flex>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            justifyContent="center"
            borderTop="1px solid"
            borderColor="gray.200"
            flexWrap="wrap"
            gap={6}
            py="32px"
          >
            <Button
              w={{ base: 'full', sm: '160px' }}
              colorScheme="gray"
              h="32px"
              fontSize="14px"
              variant="outlineDefault"
              onClick={onClose}
            >
              Cancelar
            </Button>
            <Button
              w={{ base: 'full', sm: '160px' }}
              colorScheme="gray"
              h="32px"
              fontSize="14px"
              variant="outlineDefault"
              onClick={handleSubmit(handleConfirmaSair)}
              isDisabled={isLoading || !camposHabilitados || precoNovo <= 0}
            >
              Confirmar e sair
            </Button>
            {!isAlterar && (
              <Button
                w={{ base: 'full', sm: '320px' }}
                minW="225px"
                h="32px"
                fontSize="14px"
                colorScheme="secondary"
                onClick={handleSubmit(handleConfirmarResetar)}
                isDisabled={isLoading || !camposHabilitados || precoNovo <= 0}
              >
                Confirmar e adicionar outro preço
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
