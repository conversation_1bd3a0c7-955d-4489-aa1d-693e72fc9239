import { Flex, Text } from '@chakra-ui/react';

import * as Icons from 'icons/index';

const VisualizadorIcones = () => {
  return (
    <Flex wrap="wrap" gap="4" p="4" align="center">
      {Object.entries(Icons).map(([name, IconComponent], index) => {
        if (typeof IconComponent === 'function') {
          return (
            <Flex
              key={name}
              align="center"
              justify="center"
              m="2"
              minH="40px"
              flexDir="column"
              padding="8px"
              borderRadius="6px"
              w="200px"
              h="200px"
              border="1px solid #ccc"
            >
              <IconComponent size={24} />
              <Text mt="2">{name}</Text>
              <Text mt="2">{index}</Text>
            </Flex>
          );
        } else if (typeof IconComponent === 'object') {
          return Object.entries(IconComponent).map(
            ([subName, SubIconComponent]) => (
              <Flex
                key={`${name}-${subName}`}
                align="center"
                justify="center"
                m="2"
                minH="40px"
                flexDir="column"
                padding="8px"
                borderRadius="6px"
                w="200px"
                h="200px"
                border="1px solid #ccc"
              >
                <SubIconComponent size={24} />
                <Text mt="2">{`${name} - ${subName}`}</Text>
                <Text mt="2">{index}</Text>
              </Flex>
            )
          );
        }
        return null;
      })}
    </Flex>
  );
};

export default VisualizadorIcones;
