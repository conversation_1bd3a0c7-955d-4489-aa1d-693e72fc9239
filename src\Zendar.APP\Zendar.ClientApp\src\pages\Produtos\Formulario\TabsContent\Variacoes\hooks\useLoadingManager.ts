import { useState, useCallback } from 'react';

export const useLoadingManager = () => {
  const [loadingCounter, setLoadingCounter] = useState(0);
  const isLoading = loadingCounter > 0;

  const incrementarLoading = useCallback(() => {
    setLoadingCounter((prev) => prev + 1);
  }, []);

  const decrementarLoading = useCallback(() => {
    setLoadingCounter((prev) => Math.max(0, prev - 1));
  }, []);

  const setIsLoading = useCallback(
    (loading: boolean) => {
      if (loading) {
        incrementarLoading();
      } else {
        decrementarLoading();
      }
    },
    [incrementarLoading, decrementarLoading]
  );

  const resetLoading = useCallback(() => {
    setLoadingCounter(0);
  }, []);

  return {
    isLoading,
    setIsLoading,
    incrementarLoading,
    decrementarLoading,
    resetLoading,
  };
};
